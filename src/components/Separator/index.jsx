"use client";
import React from "react";
import { motion } from "framer-motion";
import styles from "./style.module.scss";

export default function Separator({
  animated = false,
  color = "#e5e5e5",
  thickness = 1,
  className = "",
  animationDelay = 0,
  animationDuration = 0.8,
  amount = 0.6 // pourcentage visible pour déclencher l'animation
}) {
  const separatorVariants = {
    hidden: { 
      scaleX: 0,
      originX: 0 // animation de gauche à droite
    },
    visible: { 
      scaleX: 1,
      transition: {
        duration: animationDuration,
        ease: [0.33, 1, 0.68, 1],
        delay: animationDelay
      }
    }
  };

  const separatorStyle = {
    backgroundColor: color,
    height: `${thickness}px`
  };

  if (animated) {
    return (
      <motion.div
        className={`${styles.separator} ${className}`}
        style={separatorStyle}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount }}
        variants={separatorVariants}
      />
    );
  }

  return (
    <div
      className={`${styles.separator} ${className}`}
      style={separatorStyle}
    />
  );
}
